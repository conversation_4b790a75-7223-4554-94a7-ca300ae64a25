/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light mode colors */
    --bg-gradient-start: #667eea;
    --bg-gradient-end: #764ba2;
    --text-primary: #333;
    --text-secondary: #666;
    --text-muted: #adb5bd;
    --bg-glass: rgba(255, 255, 255, 0.95);
    --bg-glass-secondary: rgba(255, 255, 255, 0.9);
    --bg-accent: rgba(102, 126, 234, 0.1);
    --bg-accent-hover: rgba(102, 126, 234, 0.2);
    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --calendar-day-bg: white;
    --calendar-day-other: #f8f9fa;
    --today-bg: rgba(255, 193, 7, 0.2);
    --today-border: #ffc107;
    --selected-gradient-start: #667eea;
    --selected-gradient-end: #764ba2;
}

[data-theme="dark"] {
    /* Dark mode colors */
    --bg-gradient-start: #1a1a2e;
    --bg-gradient-end: #16213e;
    --text-primary: #e0e0e0;
    --text-secondary: #b0b0b0;
    --text-muted: #6c757d;
    --bg-glass: rgba(30, 30, 50, 0.95);
    --bg-glass-secondary: rgba(25, 25, 40, 0.9);
    --bg-accent: rgba(102, 126, 234, 0.2);
    --bg-accent-hover: rgba(102, 126, 234, 0.3);
    --border-color: #404040;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --calendar-day-bg: #2a2a3e;
    --calendar-day-other: #1e1e2e;
    --today-bg: rgba(255, 193, 7, 0.3);
    --today-border: #ffc107;
    --selected-gradient-start: #667eea;
    --selected-gradient-end: #764ba2;
}

body {
    font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
    min-height: 100vh;
    transition: all 0.3s ease;
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
header {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    color: var(--text-primary);
    padding: 2rem 0;
    text-align: center;
    margin-bottom: 2rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px var(--shadow-color);
    margin-top: 2rem;
    transition: all 0.3s ease;
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0 2rem;
}

header h1 {
    font-size: 2.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--selected-gradient-start), var(--selected-gradient-end));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.theme-toggle {
    background: var(--bg-accent);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.5rem;
}

.theme-toggle:hover {
    background: var(--bg-accent-hover);
    transform: scale(1.1);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.theme-icon {
    transition: transform 0.3s ease;
}

.theme-toggle:hover .theme-icon {
    transform: rotate(180deg);
}

.current-date-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.solar-date, .lunar-date {
    padding: 0.5rem 1rem;
    background: var(--bg-accent);
    border-radius: 25px;
    font-weight: 500;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

/* Main content */
main {
    flex: 1;
    padding: 0 0 2rem 0;
}

/* Calendar Controls */
.calendar-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px var(--shadow-color);
    transition: all 0.3s ease;
}

.nav-btn {
    background: linear-gradient(45deg, var(--selected-gradient-start), var(--selected-gradient-end));
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.month-year-display {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.month-select, .year-select {
    padding: 0.8rem 1.2rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    background: var(--calendar-day-bg);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.month-select:focus, .year-select:focus {
    outline: none;
    border-color: var(--selected-gradient-start);
    box-shadow: 0 0 0 3px var(--bg-accent);
}

/* Calendar Container */
.calendar-container {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 8px 32px var(--shadow-color);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 1rem;
}

.day-header {
    background: linear-gradient(45deg, var(--selected-gradient-start), var(--selected-gradient-end));
    color: white;
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    border-radius: 8px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

/* Calendar Day Cells */
.calendar-day {
    background: var(--calendar-day-bg);
    padding: 0.8rem 0.5rem;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    color: var(--text-primary);
}

.calendar-day:hover {
    background: var(--bg-accent-hover);
    transform: scale(1.02);
}

.calendar-day.selected {
    background: linear-gradient(45deg, var(--selected-gradient-start), var(--selected-gradient-end));
    color: white;
}

.calendar-day.other-month {
    background: var(--calendar-day-other);
    color: var(--text-muted);
}

.calendar-day.today {
    background: var(--today-bg);
    border: 2px solid var(--today-border);
}

.solar-day {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
}

.lunar-day {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.2;
}

.calendar-day.selected .lunar-day {
    color: rgba(255, 255, 255, 0.9);
}

/* Info Panel */
.info-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.selected-date-info, .lunar-info {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px var(--shadow-color);
    transition: all 0.3s ease;
}

.selected-date-info h3, .lunar-info h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 600;
}

.info-grid {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem;
    background: var(--bg-accent);
    border-radius: 8px;
    border-left: 4px solid var(--selected-gradient-start);
    transition: all 0.3s ease;
}

.info-item .label {
    font-weight: 500;
    color: var(--text-secondary);
}

.info-item .value {
    font-weight: 600;
    color: var(--text-primary);
}

/* Footer */
footer {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    color: var(--text-primary);
    text-align: center;
    padding: 1.5rem 0;
    margin-top: 2rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px var(--shadow-color);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

/* Responsive design */
@media (max-width: 1024px) {
    .info-panel {
        grid-template-columns: 1fr;
    }

    .calendar-controls {
        flex-wrap: wrap;
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }

    .header-top {
        padding: 0 1rem;
    }

    header h1 {
        font-size: 2rem;
    }

    .theme-toggle {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .current-date-info {
        flex-direction: column;
        gap: 1rem;
    }

    .calendar-container {
        padding: 1rem;
    }

    .calendar-day {
        min-height: 60px;
        padding: 0.5rem 0.3rem;
    }

    .solar-day {
        font-size: 1rem;
    }

    .lunar-day {
        font-size: 0.7rem;
    }

    .nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .selected-date-info, .lunar-info {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .header-top {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    header h1 {
        font-size: 1.8rem;
    }

    .calendar-day {
        min-height: 50px;
        padding: 0.3rem 0.2rem;
    }

    .solar-day {
        font-size: 0.9rem;
    }

    .lunar-day {
        font-size: 0.6rem;
    }

    .day-header {
        padding: 0.8rem 0.5rem;
        font-size: 0.9rem;
    }

    .calendar-controls {
        flex-direction: column;
        gap: 1rem;
    }

    .month-year-display {
        order: -1;
    }
}
