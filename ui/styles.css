/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    color: #2c3e50;
    padding: 2rem 0;
    text-align: center;
    margin-bottom: 2rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-top: 2rem;
}

header h1 {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.current-date-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.solar-date, .lunar-date {
    padding: 0.5rem 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 25px;
    font-weight: 500;
}

/* Main content */
main {
    flex: 1;
    padding: 0 0 2rem 0;
}

/* Calendar Controls */
.calendar-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.nav-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.month-year-display {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.month-select, .year-select {
    padding: 0.8rem 1.2rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.month-select:focus, .year-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Calendar Container */
.calendar-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 1rem;
}

.day-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    border-radius: 8px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

/* Calendar Day Cells */
.calendar-day {
    background: white;
    padding: 0.8rem 0.5rem;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.calendar-day:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

.calendar-day.selected {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.calendar-day.other-month {
    background: #f8f9fa;
    color: #adb5bd;
}

.calendar-day.today {
    background: rgba(255, 193, 7, 0.2);
    border: 2px solid #ffc107;
}

.solar-day {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
}

.lunar-day {
    font-size: 0.8rem;
    color: #666;
    text-align: center;
    line-height: 1.2;
}

.calendar-day.selected .lunar-day {
    color: rgba(255, 255, 255, 0.9);
}

/* Info Panel */
.info-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.selected-date-info, .lunar-info {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.selected-date-info h3, .lunar-info h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 600;
}

.info-grid {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.info-item .label {
    font-weight: 500;
    color: #555;
}

.info-item .value {
    font-weight: 600;
    color: #2c3e50;
}

/* Footer */
footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    color: #2c3e50;
    text-align: center;
    padding: 1.5rem 0;
    margin-top: 2rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

/* Responsive design */
@media (max-width: 1024px) {
    .info-panel {
        grid-template-columns: 1fr;
    }

    .calendar-controls {
        flex-wrap: wrap;
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    .current-date-info {
        flex-direction: column;
        gap: 1rem;
    }

    .calendar-container {
        padding: 1rem;
    }

    .calendar-day {
        min-height: 60px;
        padding: 0.5rem 0.3rem;
    }

    .solar-day {
        font-size: 1rem;
    }

    .lunar-day {
        font-size: 0.7rem;
    }

    .nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .selected-date-info, .lunar-info {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .calendar-day {
        min-height: 50px;
        padding: 0.3rem 0.2rem;
    }

    .solar-day {
        font-size: 0.9rem;
    }

    .lunar-day {
        font-size: 0.6rem;
    }

    .day-header {
        padding: 0.8rem 0.5rem;
        font-size: 0.9rem;
    }
}
