/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light mode colors */
    --bg-gradient-start: #667eea;
    --bg-gradient-end: #764ba2;
    --text-primary: #333;
    --text-secondary: #666;
    --text-muted: #adb5bd;
    --bg-glass: rgba(255, 255, 255, 0.95);
    --bg-glass-secondary: rgba(255, 255, 255, 0.9);
    --bg-accent: rgba(102, 126, 234, 0.1);
    --bg-accent-hover: rgba(102, 126, 234, 0.2);
    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --calendar-day-bg: white;
    --calendar-day-other: #f8f9fa;
    --today-bg: rgba(255, 193, 7, 0.2);
    --today-border: #ffc107;
    --selected-gradient-start: #667eea;
    --selected-gradient-end: #764ba2;
}

[data-theme="dark"] {
    /* Dark mode colors */
    --bg-gradient-start: #1a1a2e;
    --bg-gradient-end: #16213e;
    --text-primary: #e0e0e0;
    --text-secondary: #b0b0b0;
    --text-muted: #6c757d;
    --bg-glass: rgba(30, 30, 50, 0.95);
    --bg-glass-secondary: rgba(25, 25, 40, 0.9);
    --bg-accent: rgba(102, 126, 234, 0.2);
    --bg-accent-hover: rgba(102, 126, 234, 0.3);
    --border-color: #404040;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --calendar-day-bg: #2a2a3e;
    --calendar-day-other: #1e1e2e;
    --today-bg: rgba(255, 193, 7, 0.3);
    --today-border: #ffc107;
    --selected-gradient-start: #667eea;
    --selected-gradient-end: #764ba2;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
    background-attachment: fixed;
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* Scroll Container */
.scroll-container {
    height: 100vh;
    overflow-y: auto;
    scroll-snap-type: y mandatory;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Hide scrollbar but keep functionality */
.scroll-container::-webkit-scrollbar {
    display: none;
}

.scroll-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

/* Page Sections */
.page {
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
    scroll-snap-align: start;
    display: flex;
    align-items: stretch;
    justify-content: center;
    padding: 1rem;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
}

.page-content {
    width: 100%;
    max-width: 1200px;
    height: calc(100vh - 2rem);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    overflow: hidden;
    padding: 1rem 0;
}

/* Navigation Dots */
.nav-dots {
    position: fixed;
    right: 2rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.nav-dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.nav-dot:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
    border-color: rgba(255, 255, 255, 0.6);
}

.nav-dot.active {
    background: var(--selected-gradient-start);
    border-color: white;
    transform: scale(1.3);
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

/* Fixed Theme Toggle */
.theme-toggle-fixed {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1001;
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.5rem;
    box-shadow: 0 4px 20px var(--shadow-color);
}

.theme-toggle-fixed:hover {
    background: var(--bg-accent-hover);
    transform: scale(1.1);
    box-shadow: 0 8px 30px var(--shadow-color);
    border-color: var(--selected-gradient-start);
}

.theme-icon {
    transition: transform 0.3s ease;
}

.theme-toggle-fixed:hover .theme-icon {
    transform: rotate(180deg);
}

/* Scroll Hints */
.scroll-hint {
    position: absolute;
    bottom: 0.8rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
    color: var(--text-secondary);
    font-size: 0.7rem;
    animation: bounce 2s infinite;
    z-index: 10;
    opacity: 0.8;
}

.scroll-hint-up {
    top: 0.8rem;
    bottom: auto;
    animation: bounceUp 2s infinite;
}

.scroll-arrow {
    font-size: 1.5rem;
    font-weight: bold;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

@keyframes bounceUp {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(10px); }
    60% { transform: translateX(-50%) translateY(5px); }
}

/* Today Page Styles */
.today-page {
    position: relative;
    padding: 0.5rem;
}

.today-page .page-content {
    gap: 1rem;
    height: calc(100vh - 1rem);
    padding: 0.5rem 0;
}

.today-header {
    text-align: center;
    flex-shrink: 0;
    margin-bottom: 0.5rem;
}

.today-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--selected-gradient-start), var(--selected-gradient-end));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.today-date-large {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 400;
    opacity: 0.9;
}

.today-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    width: 100%;
    max-width: 900px;
    flex: 1;
    align-content: center;
    min-height: 0;
}

.today-card {
    background: var(--bg-glass);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    padding: 1.2rem;
    text-align: center;
    box-shadow: 0 8px 32px var(--shadow-color);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    height: fit-content;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

.today-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--selected-gradient-start), var(--selected-gradient-end));
}

.today-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px var(--shadow-color);
    border-color: rgba(255, 255, 255, 0.3);
}

.card-icon {
    font-size: 2.2rem;
    margin-bottom: 0.8rem;
    display: block;
    flex-shrink: 0;
}

.today-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    flex-shrink: 0;
}

.card-content {
    display: flex;
    flex-direction: column;
    gap: 0.6rem;
    flex: 1;
    min-height: 0;
}

.date-display {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--selected-gradient-start);
    margin-bottom: 0.2rem;
}

.day-name, .zodiac-info, .season-info {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.canchi-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.canchi-item:last-child {
    border-bottom: none;
}

.canchi-item .label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.8rem;
}

.canchi-item .value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.85rem;
}

.solar-term {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--selected-gradient-end);
    margin-bottom: 0.2rem;
}

/* Calendar Page Styles */
.calendar-page {
    position: relative;
    padding: 0.5rem;
}

.calendar-page .page-content {
    gap: 1rem;
    height: calc(100vh - 1rem);
    padding: 0.5rem 0;
}

.calendar-header-section {
    text-align: center;
    flex-shrink: 0;
}

.calendar-title {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--selected-gradient-start), var(--selected-gradient-end));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Calendar Controls */
.calendar-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    padding: 0.8rem;
    border-radius: 12px;
    box-shadow: 0 6px 24px var(--shadow-color);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.nav-btn {
    background: linear-gradient(45deg, var(--selected-gradient-start), var(--selected-gradient-end));
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.month-year-display {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.month-select, .year-select {
    padding: 0.8rem 1.2rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    background: var(--calendar-day-bg);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.month-select:focus, .year-select:focus {
    outline: none;
    border-color: var(--selected-gradient-start);
    box-shadow: 0 0 0 3px var(--bg-accent);
}

/* Calendar Container */
.calendar-container {
    background: var(--bg-glass);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    padding: 1rem;
    box-shadow: 0 6px 24px var(--shadow-color);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 800px;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
}

.calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
}

.day-header {
    background: linear-gradient(45deg, var(--selected-gradient-start), var(--selected-gradient-end));
    color: white;
    padding: 0.8rem;
    text-align: center;
    font-weight: 600;
    border-radius: 8px;
    font-size: 0.85rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    background: var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    padding: 2px;
    flex: 1;
}

/* Calendar Day Cells */
.calendar-day {
    background: var(--calendar-day-bg);
    padding: 0.5rem 0.3rem;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    color: var(--text-primary);
    border-radius: 6px;
    border: 1px solid transparent;
    aspect-ratio: 1;
    max-height: 80px;
}

.calendar-day:hover {
    background: var(--bg-accent-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-color);
    border-color: var(--selected-gradient-start);
}

.calendar-day.selected {
    background: linear-gradient(45deg, var(--selected-gradient-start), var(--selected-gradient-end));
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.calendar-day.other-month {
    background: var(--calendar-day-other);
    color: var(--text-muted);
    opacity: 0.6;
}

.calendar-day.today {
    background: var(--today-bg);
    border: 2px solid var(--today-border);
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.3);
}

.solar-day {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.lunar-day {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.2;
    font-weight: 500;
}

.calendar-day.selected .lunar-day {
    color: rgba(255, 255, 255, 0.9);
}

.calendar-day.other-month .lunar-day {
    opacity: 0.7;
}



/* Responsive design */
@media (max-width: 1024px) {
    .today-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .calendar-controls {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .nav-dots {
        right: 1rem;
    }

    .theme-toggle-fixed {
        top: 1rem;
        right: 1rem;
        width: 50px;
        height: 50px;
    }
}

@media (max-width: 768px) {
    .page {
        padding: 0.5rem;
    }

    .page-content {
        height: calc(100vh - 1rem);
        padding: 0.5rem 0;
    }

    .today-page {
        padding: 0.5rem;
    }

    .calendar-page {
        padding: 0.5rem;
    }

    .today-title {
        font-size: 2rem;
    }

    .calendar-title {
        font-size: 1.5rem;
    }

    .today-info-grid {
        grid-template-columns: 1fr;
        gap: 0.8rem;
        max-width: 350px;
    }

    .today-card {
        padding: 1rem;
    }

    .date-display {
        font-size: 1.4rem;
    }

    .calendar-container {
        padding: 1rem;
    }

    .calendar-day {
        min-height: 60px;
        padding: 0.6rem 0.3rem;
    }

    .solar-day {
        font-size: 1rem;
    }

    .lunar-day {
        font-size: 0.65rem;
    }

    .nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .nav-dots {
        display: none;
    }

    .scroll-hint {
        font-size: 0.75rem;
        bottom: 1rem;
    }

    .scroll-hint-up {
        top: 1rem;
    }

    .theme-toggle-fixed {
        top: 1rem;
        right: 1rem;
        width: 50px;
        height: 50px;
    }
}

@media (max-width: 480px) {
    .page {
        padding: 0.3rem;
    }

    .page-content {
        height: calc(100vh - 0.6rem);
        padding: 0.3rem 0;
    }

    .today-page {
        padding: 0.3rem;
    }

    .calendar-page {
        padding: 0.3rem;
    }

    .today-title {
        font-size: 1.8rem;
    }

    .calendar-title {
        font-size: 1.3rem;
    }

    .today-info-grid {
        max-width: 320px;
        gap: 0.6rem;
    }

    .today-card {
        padding: 0.8rem;
    }

    .card-icon {
        font-size: 2rem;
    }

    .date-display {
        font-size: 1.2rem;
    }

    .calendar-container {
        padding: 1rem;
    }

    .calendar-day {
        min-height: 60px;
        padding: 0.5rem 0.3rem;
    }

    .solar-day {
        font-size: 1rem;
    }

    .lunar-day {
        font-size: 0.65rem;
    }

    .day-header {
        padding: 0.8rem 0.3rem;
        font-size: 0.8rem;
    }

    .calendar-controls {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .month-year-display {
        order: -1;
    }

    .theme-toggle-fixed {
        top: 1rem;
        right: 1rem;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .scroll-hint {
        bottom: 1rem;
        font-size: 0.75rem;
    }

    .scroll-hint-up {
        top: 1rem;
    }

    .today-header {
        margin-bottom: 2rem;
    }

    .today-date-large {
        font-size: 1.2rem;
    }
}
