// Lunar UI JavaScript

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    console.log('Lunar UI loaded successfully');
    
    // Initialize the application
    initializeApp();
});

// Initialize application
function initializeApp() {
    // Add any initialization logic here
    console.log('Initializing Lunar application...');
    
    // Example: Add click event listeners
    setupEventListeners();
    
    // Example: Load initial data
    loadInitialData();
}

// Setup event listeners
function setupEventListeners() {
    // Add event listeners for UI interactions
    console.log('Setting up event listeners...');
    
    // Example: Header click event
    const header = document.querySelector('header h1');
    if (header) {
        header.addEventListener('click', function() {
            console.log('Header clicked!');
        });
    }
}

// Load initial data
function loadInitialData() {
    console.log('Loading initial data...');
    
    // Add logic to load data from your Go backend
    // Example API call structure:
    /*
    fetch('/api/data')
        .then(response => response.json())
        .then(data => {
            console.log('Data loaded:', data);
            // Update UI with data
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });
    */
}

// Utility functions
const Utils = {
    // Format date
    formatDate: function(date) {
        return new Date(date).toLocaleDateString();
    },
    
    // Show notification
    showNotification: function(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        // Add actual notification logic here
    },
    
    // API call helper
    apiCall: async function(endpoint, options = {}) {
        try {
            const response = await fetch(endpoint, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }
};

// Export for use in other modules if needed
window.LunarUI = {
    Utils,
    initializeApp
};
