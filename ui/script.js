// Lunar Calendar Application JavaScript

class LunarCalendarApp {
    constructor() {
        this.lunarCalendar = new LunarCalendar();
        this.currentDate = new Date();
        this.selectedDate = null;
        this.currentMonth = this.currentDate.getMonth();
        this.currentYear = this.currentDate.getFullYear();

        this.monthNames = [
            'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
            'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
        ];
    }

    // Initialize the application
    init() {
        console.log('Initializing Lunar Calendar Application...');
        this.setupEventListeners();
        this.populateSelectors();
        this.updateCurrentDateInfo();
        this.renderCalendar();
    }

    // Setup event listeners
    setupEventListeners() {
        // Navigation buttons
        document.getElementById('prevMonth').addEventListener('click', () => this.previousMonth());
        document.getElementById('nextMonth').addEventListener('click', () => this.nextMonth());

        // Month and year selectors
        document.getElementById('monthSelect').addEventListener('change', (e) => {
            this.currentMonth = parseInt(e.target.value);
            this.renderCalendar();
        });

        document.getElementById('yearSelect').addEventListener('change', (e) => {
            this.currentYear = parseInt(e.target.value);
            this.renderCalendar();
        });
    }

    // Populate month and year selectors
    populateSelectors() {
        const monthSelect = document.getElementById('monthSelect');
        const yearSelect = document.getElementById('yearSelect');

        // Populate months
        this.monthNames.forEach((month, index) => {
            const option = document.createElement('option');
            option.value = index;
            option.textContent = month;
            if (index === this.currentMonth) {
                option.selected = true;
            }
            monthSelect.appendChild(option);
        });

        // Populate years (from 1900 to 2100)
        for (let year = 1900; year <= 2100; year++) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year;
            if (year === this.currentYear) {
                option.selected = true;
            }
            yearSelect.appendChild(option);
        }
    }

    // Update current date information
    updateCurrentDateInfo() {
        const today = new Date();
        const lunarToday = this.lunarCalendar.solarToLunar(
            today.getFullYear(),
            today.getMonth() + 1,
            today.getDate()
        );

        // Format solar date
        const solarDateStr = today.toLocaleDateString('vi-VN', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Format lunar date
        const lunarDateStr = this.lunarCalendar.formatLunarDate(lunarToday);
        const zodiac = this.lunarCalendar.getZodiac(lunarToday.year);

        document.getElementById('currentSolarDate').textContent = `Dương lịch: ${solarDateStr}`;
        document.getElementById('currentLunarDate').textContent = `Âm lịch: ${lunarDateStr} - Năm ${zodiac}`;
    }

    // Navigate to previous month
    previousMonth() {
        if (this.currentMonth === 0) {
            this.currentMonth = 11;
            this.currentYear--;
        } else {
            this.currentMonth--;
        }
        this.updateSelectors();
        this.renderCalendar();
    }

    // Navigate to next month
    nextMonth() {
        if (this.currentMonth === 11) {
            this.currentMonth = 0;
            this.currentYear++;
        } else {
            this.currentMonth++;
        }
        this.updateSelectors();
        this.renderCalendar();
    }

    // Update selectors to match current month/year
    updateSelectors() {
        document.getElementById('monthSelect').value = this.currentMonth;
        document.getElementById('yearSelect').value = this.currentYear;
    }

    // Render the calendar
    renderCalendar() {
        const calendarGrid = document.getElementById('calendarGrid');
        calendarGrid.innerHTML = '';

        // Get first day of month and number of days
        const firstDay = new Date(this.currentYear, this.currentMonth, 1);
        const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        // Get previous month's last days
        const prevMonth = new Date(this.currentYear, this.currentMonth, 0);
        const daysInPrevMonth = prevMonth.getDate();

        // Add previous month's trailing days
        for (let i = startingDayOfWeek - 1; i >= 0; i--) {
            const day = daysInPrevMonth - i;
            const prevMonthDate = new Date(this.currentYear, this.currentMonth - 1, day);
            this.createDayCell(prevMonthDate, true);
        }

        // Add current month's days
        for (let day = 1; day <= daysInMonth; day++) {
            const currentDate = new Date(this.currentYear, this.currentMonth, day);
            this.createDayCell(currentDate, false);
        }

        // Add next month's leading days
        const totalCells = calendarGrid.children.length;
        const remainingCells = 42 - totalCells; // 6 rows × 7 days
        for (let day = 1; day <= remainingCells; day++) {
            const nextMonthDate = new Date(this.currentYear, this.currentMonth + 1, day);
            this.createDayCell(nextMonthDate, true);
        }
    }
    // Create a day cell
    createDayCell(date, isOtherMonth) {
        const calendarGrid = document.getElementById('calendarGrid');
        const dayCell = document.createElement('div');
        dayCell.className = 'calendar-day';

        if (isOtherMonth) {
            dayCell.classList.add('other-month');
        }

        // Check if it's today
        const today = new Date();
        if (date.toDateString() === today.toDateString()) {
            dayCell.classList.add('today');
        }

        // Solar day
        const solarDay = document.createElement('div');
        solarDay.className = 'solar-day';
        solarDay.textContent = date.getDate();

        // Lunar day
        const lunarDate = this.lunarCalendar.solarToLunar(
            date.getFullYear(),
            date.getMonth() + 1,
            date.getDate()
        );

        const lunarDay = document.createElement('div');
        lunarDay.className = 'lunar-day';

        // Show lunar month name on first day, otherwise just day number
        if (lunarDate.day === 1) {
            const monthName = this.lunarCalendar.lunarMonths[lunarDate.month - 1];
            const prefix = lunarDate.isLeapMonth ? "N." : "";
            lunarDay.textContent = `${prefix}${monthName.replace('Tháng ', '')}`;
        } else {
            lunarDay.textContent = lunarDate.day;
        }

        dayCell.appendChild(solarDay);
        dayCell.appendChild(lunarDay);

        // Add click event
        dayCell.addEventListener('click', () => this.selectDate(date));

        calendarGrid.appendChild(dayCell);
    }

    // Select a date
    selectDate(date) {
        // Remove previous selection
        const prevSelected = document.querySelector('.calendar-day.selected');
        if (prevSelected) {
            prevSelected.classList.remove('selected');
        }

        // Add selection to clicked day
        event.target.closest('.calendar-day').classList.add('selected');

        this.selectedDate = date;
        this.updateSelectedDateInfo();
    }

    // Update selected date information
    updateSelectedDateInfo() {
        if (!this.selectedDate) return;

        const lunarDate = this.lunarCalendar.solarToLunar(
            this.selectedDate.getFullYear(),
            this.selectedDate.getMonth() + 1,
            this.selectedDate.getDate()
        );

        // Format solar date
        const solarDateStr = this.selectedDate.toLocaleDateString('vi-VN', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Format lunar date
        const lunarDateStr = this.lunarCalendar.formatLunarDate(lunarDate);

        // Get Can Chi information
        const yearCanChi = this.lunarCalendar.getYearCanChi(lunarDate.year);
        const monthCanChi = this.lunarCalendar.getMonthCanChi(lunarDate.year, lunarDate.month);
        const dayCanChi = this.lunarCalendar.getDayCanChi(
            this.selectedDate.getFullYear(),
            this.selectedDate.getMonth() + 1,
            this.selectedDate.getDate()
        );

        // Get zodiac and solar term
        const zodiac = this.lunarCalendar.getZodiac(lunarDate.year);
        const solarTerm = this.lunarCalendar.getSolarTerm(
            this.selectedDate.getMonth() + 1,
            this.selectedDate.getDate()
        );

        // Update UI
        const selectedDateInfo = document.getElementById('selectedDateInfo');
        selectedDateInfo.innerHTML = `
            <h3>Thông tin ngày được chọn</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="label">Dương lịch:</span>
                    <span class="value">${solarDateStr}</span>
                </div>
                <div class="info-item">
                    <span class="label">Âm lịch:</span>
                    <span class="value">${lunarDateStr}</span>
                </div>
                <div class="info-item">
                    <span class="label">Năm con:</span>
                    <span class="value">${zodiac} (${lunarDate.year})</span>
                </div>
            </div>
        `;

        // Update Can Chi information
        document.getElementById('yearCanChi').textContent = `${yearCanChi} (${zodiac})`;
        document.getElementById('monthCanChi').textContent = monthCanChi;
        document.getElementById('dayCanChi').textContent = dayCanChi;
        document.getElementById('solarTerm').textContent = solarTerm;
    }

    // Utility function to format Vietnamese date
    formatVietnameseDate(date) {
        const days = ['Chủ nhật', 'Thứ hai', 'Thứ ba', 'Thứ tư', 'Thứ năm', 'Thứ sáu', 'Thứ bảy'];
        const months = [
            'tháng 1', 'tháng 2', 'tháng 3', 'tháng 4', 'tháng 5', 'tháng 6',
            'tháng 7', 'tháng 8', 'tháng 9', 'tháng 10', 'tháng 11', 'tháng 12'
        ];

        const dayName = days[date.getDay()];
        const day = date.getDate();
        const month = months[date.getMonth()];
        const year = date.getFullYear();

        return `${dayName}, ${day} ${month} ${year}`;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Lunar Calendar loaded successfully');

    const app = new LunarCalendarApp();
    app.init();

    // Make app globally available for debugging
    window.lunarApp = app;
});

// Utility functions
const Utils = {
    // Format date
    formatDate: function(date) {
        return new Date(date).toLocaleDateString('vi-VN');
    },

    // Show notification
    showNotification: function(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        // Add actual notification logic here
    },

    // Get lunar date for any solar date
    getLunarDate: function(year, month, day) {
        const lunarCalendar = new LunarCalendar();
        return lunarCalendar.solarToLunar(year, month, day);
    }
};

// Export for use in other modules if needed
window.LunarUI = {
    Utils,
    LunarCalendarApp
};
