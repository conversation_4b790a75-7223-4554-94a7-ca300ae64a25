<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lunar Dashboard - Sub UI</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Background Animation -->
    <div class="bg-animation">
        <div class="star"></div>
        <div class="star"></div>
        <div class="star"></div>
        <div class="star"></div>
        <div class="star"></div>
        <div class="moon"></div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <span class="logo-icon">🌙</span>
                    <h1>Lunar Dashboard</h1>
                </div>
                <nav class="nav">
                    <a href="#home" class="nav-link active">Trang chủ</a>
                    <a href="#analytics" class="nav-link">Phân tích</a>
                    <a href="#calendar" class="nav-link">Lịch</a>
                    <a href="#settings" class="nav-link">Cài đặt</a>
                </nav>
                <button class="theme-toggle" id="themeToggle">
                    <span class="theme-icon">🌙</span>
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main">
            <!-- Hero Section -->
            <section class="hero">
                <div class="hero-content">
                    <h2 class="hero-title">Chào mừng đến với Lunar Dashboard</h2>
                    <p class="hero-subtitle">Trải nghiệm giao diện hiện đại với thiết kế full screen</p>
                    <div class="hero-stats">
                        <div class="stat-card">
                            <div class="stat-number" id="currentTime">00:00:00</div>
                            <div class="stat-label">Thời gian hiện tại</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="currentDate">01/01/2024</div>
                            <div class="stat-label">Ngày hôm nay</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="lunarDate">Tháng Giêng</div>
                            <div class="stat-label">Âm lịch</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Features Grid -->
            <section class="features">
                <div class="features-grid">
                    <div class="feature-card" data-feature="weather">
                        <div class="feature-icon">🌤️</div>
                        <h3>Thời tiết</h3>
                        <p>Theo dõi thời tiết theo thời gian thực</p>
                        <div class="feature-action">
                            <button class="btn-primary">Xem chi tiết</button>
                        </div>
                    </div>

                    <div class="feature-card" data-feature="calendar">
                        <div class="feature-icon">📅</div>
                        <h3>Lịch âm dương</h3>
                        <p>Chuyển đổi và xem lịch âm dương</p>
                        <div class="feature-action">
                            <button class="btn-primary">Mở lịch</button>
                        </div>
                    </div>

                    <div class="feature-card" data-feature="analytics">
                        <div class="feature-icon">📊</div>
                        <h3>Thống kê</h3>
                        <p>Phân tích dữ liệu và báo cáo</p>
                        <div class="feature-action">
                            <button class="btn-primary">Xem báo cáo</button>
                        </div>
                    </div>

                    <div class="feature-card" data-feature="tools">
                        <div class="feature-icon">🛠️</div>
                        <h3>Công cụ</h3>
                        <p>Các tiện ích và công cụ hữu ích</p>
                        <div class="feature-action">
                            <button class="btn-primary">Khám phá</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <h3>Thao tác nhanh</h3>
                <div class="actions-grid">
                    <button class="action-btn" data-action="fullscreen">
                        <span class="action-icon">⛶</span>
                        <span>Full Screen</span>
                    </button>
                    <button class="action-btn" data-action="refresh">
                        <span class="action-icon">🔄</span>
                        <span>Làm mới</span>
                    </button>
                    <button class="action-btn" data-action="share">
                        <span class="action-icon">📤</span>
                        <span>Chia sẻ</span>
                    </button>
                    <button class="action-btn" data-action="download">
                        <span class="action-icon">💾</span>
                        <span>Tải xuống</span>
                    </button>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <p>&copy; 2024 Lunar Dashboard. Thiết kế bởi Sub-UI System.</p>
                <div class="footer-links">
                    <a href="#privacy">Chính sách</a>
                    <a href="#terms">Điều khoản</a>
                    <a href="#contact">Liên hệ</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Đang tải...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>
