// Lunar Dashboard Sub-UI JavaScript

class LunarDashboard {
    constructor() {
        this.isDarkMode = true;
        this.currentTime = new Date();
        this.isFullscreen = false;
        
        this.init();
    }

    init() {
        console.log('🌙 Lunar Dashboard Sub-UI initialized');
        
        this.setupEventListeners();
        this.startClock();
        this.updateDates();
        this.hideLoadingOverlay();
        this.setupAnimations();
    }

    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        themeToggle?.addEventListener('click', () => this.toggleTheme());

        // Navigation links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => this.handleNavigation(e));
        });

        // Feature cards
        const featureCards = document.querySelectorAll('.feature-card');
        featureCards.forEach(card => {
            card.addEventListener('click', () => this.handleFeatureClick(card));
        });

        // Action buttons
        const actionBtns = document.querySelectorAll('.action-btn');
        actionBtns.forEach(btn => {
            btn.addEventListener('click', () => this.handleActionClick(btn));
        });

        // Primary buttons
        const primaryBtns = document.querySelectorAll('.btn-primary');
        primaryBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.handlePrimaryButtonClick(e));
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));

        // Window resize
        window.addEventListener('resize', () => this.handleResize());
    }

    toggleTheme() {
        this.isDarkMode = !this.isDarkMode;
        const body = document.body;
        const themeIcon = document.querySelector('.theme-icon');
        
        if (this.isDarkMode) {
            body.removeAttribute('data-theme');
            themeIcon.textContent = '🌙';
        } else {
            body.setAttribute('data-theme', 'light');
            themeIcon.textContent = '☀️';
        }
        
        this.showNotification(`Đã chuyển sang ${this.isDarkMode ? 'chế độ tối' : 'chế độ sáng'}`);
    }

    handleNavigation(e) {
        e.preventDefault();
        
        // Remove active class from all nav links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // Add active class to clicked link
        e.target.classList.add('active');
        
        const href = e.target.getAttribute('href');
        this.showNotification(`Điều hướng đến: ${href.replace('#', '')}`);
        
        // Smooth scroll to section if exists
        const targetSection = document.querySelector(href);
        if (targetSection) {
            targetSection.scrollIntoView({ behavior: 'smooth' });
        }
    }

    handleFeatureClick(card) {
        const feature = card.getAttribute('data-feature');
        
        // Add click animation
        card.style.transform = 'scale(0.95)';
        setTimeout(() => {
            card.style.transform = '';
        }, 150);
        
        this.showNotification(`Đã chọn tính năng: ${feature}`);
        
        // Simulate feature loading
        this.showLoading();
        setTimeout(() => {
            this.hideLoading();
            this.showNotification(`Tính năng ${feature} đã sẵn sàng!`);
        }, 2000);
    }

    handleActionClick(btn) {
        const action = btn.getAttribute('data-action');
        
        switch (action) {
            case 'fullscreen':
                this.toggleFullscreen();
                break;
            case 'refresh':
                this.refreshPage();
                break;
            case 'share':
                this.shareContent();
                break;
            case 'download':
                this.downloadContent();
                break;
            default:
                this.showNotification(`Thực hiện hành động: ${action}`);
        }
    }

    handlePrimaryButtonClick(e) {
        e.preventDefault();
        const buttonText = e.target.textContent;
        
        // Button animation
        e.target.style.transform = 'scale(0.95)';
        setTimeout(() => {
            e.target.style.transform = '';
        }, 150);
        
        this.showNotification(`Đã nhấn: ${buttonText}`);
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + F for fullscreen
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            this.toggleFullscreen();
        }
        
        // Ctrl/Cmd + R for refresh
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            this.refreshPage();
        }
        
        // T for theme toggle
        if (e.key === 't' || e.key === 'T') {
            this.toggleTheme();
        }
    }

    handleResize() {
        // Handle responsive adjustments
        const width = window.innerWidth;
        
        if (width < 768) {
            this.showNotification('Chế độ mobile được kích hoạt');
        }
    }

    startClock() {
        const updateClock = () => {
            this.currentTime = new Date();
            const timeElement = document.getElementById('currentTime');
            
            if (timeElement) {
                const timeString = this.currentTime.toLocaleTimeString('vi-VN');
                timeElement.textContent = timeString;
            }
        };
        
        updateClock();
        setInterval(updateClock, 1000);
    }

    updateDates() {
        const dateElement = document.getElementById('currentDate');
        const lunarElement = document.getElementById('lunarDate');
        
        if (dateElement) {
            const dateString = this.currentTime.toLocaleDateString('vi-VN');
            dateElement.textContent = dateString;
        }
        
        if (lunarElement) {
            // Simplified lunar date (would need actual lunar calendar calculation)
            const lunarMonths = [
                'Tháng Giêng', 'Tháng Hai', 'Tháng Ba', 'Tháng Tư',
                'Tháng Năm', 'Tháng Sáu', 'Tháng Bảy', 'Tháng Tám',
                'Tháng Chín', 'Tháng Mười', 'Tháng Mười Một', 'Tháng Chạp'
            ];
            const currentMonth = this.currentTime.getMonth();
            lunarElement.textContent = lunarMonths[currentMonth];
        }
    }

    toggleFullscreen() {
        if (!this.isFullscreen) {
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen();
            }
            this.isFullscreen = true;
            this.showNotification('Đã bật chế độ toàn màn hình');
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            }
            this.isFullscreen = false;
            this.showNotification('Đã thoát chế độ toàn màn hình');
        }
    }

    refreshPage() {
        this.showLoading();
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    shareContent() {
        if (navigator.share) {
            navigator.share({
                title: 'Lunar Dashboard',
                text: 'Trải nghiệm giao diện hiện đại với Lunar Dashboard',
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href);
            this.showNotification('Đã sao chép liên kết vào clipboard');
        }
    }

    downloadContent() {
        // Simulate download
        this.showNotification('Đang chuẩn bị tải xuống...');
        
        setTimeout(() => {
            const blob = new Blob(['Lunar Dashboard Data'], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'lunar-dashboard-data.txt';
            a.click();
            URL.revokeObjectURL(url);
            
            this.showNotification('Tải xuống hoàn tất!');
        }, 1500);
    }

    setupAnimations() {
        // Intersection Observer for scroll animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        // Observe elements for animation
        const animatedElements = document.querySelectorAll('.feature-card, .stat-card');
        animatedElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'all 0.6s ease';
            observer.observe(el);
        });
    }

    showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('active');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }

    hideLoadingOverlay() {
        // Hide initial loading overlay
        setTimeout(() => {
            this.hideLoading();
        }, 1000);
    }

    showNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            color: var(--text-primary);
            padding: 1rem 2rem;
            border-radius: 10px;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Initialize the dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.lunarDashboard = new LunarDashboard();
});

// Export for potential external use
window.LunarDashboard = LunarDashboard;
