// Lunar Calendar Conversion Library
// Based on Vietnamese Lunar Calendar System

class LunarCalendar {
    constructor() {
        // Can (<PERSON>hiê<PERSON> can)
        this.can = ["<PERSON><PERSON><PERSON><PERSON>", "Ất", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kỷ", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"];
        
        // Chi (Đ<PERSON>a chi)
        this.chi = ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tỵ", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ất", "<PERSON><PERSON><PERSON>"];
        
        // Zodiac animals
        this.zodiac = ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ựa", "<PERSON><PERSON>", "Khỉ", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"];
        
        // Solar terms (24 tiết khí)
        this.solarTerms = [
            "<PERSON>ậ<PERSON> xuân", "<PERSON><PERSON> thủy", "<PERSON><PERSON> trập", "<PERSON><PERSON> phân", "<PERSON><PERSON> <PERSON>h", "<PERSON><PERSON><PERSON> vũ",
            "<PERSON><PERSON><PERSON> hạ", "<PERSON><PERSON><PERSON><PERSON> mãn", "<PERSON><PERSON> chủng", "<PERSON><PERSON> chí", "<PERSON><PERSON><PERSON><PERSON> thử", "<PERSON><PERSON>i th<PERSON>",
            "<PERSON><PERSON>p thu", "Xử thử", "Bạch lộ", "Thu phân", "Hàn lộ", "Sương giáng",
            "Lập đông", "Tiểu tuyết", "Đại tuyết", "Đông chí", "Tiểu hàn", "Đại hàn"
        ];
        
        // Lunar months names
        this.lunarMonths = [
            "Tháng Giêng", "Tháng Hai", "Tháng Ba", "Tháng Tư", "Tháng Năm", "Tháng Sáu",
            "Tháng Bảy", "Tháng Tám", "Tháng Chín", "Tháng Mười", "Tháng Mười Một", "Tháng Chạp"
        ];
    }

    // Convert Julian Day Number to date
    jdToDate(jd) {
        const a = jd + 32044;
        const b = Math.floor((4 * a + 3) / 146097);
        const c = a - Math.floor((b * 146097) / 4);
        const d = Math.floor((4 * c + 3) / 1461);
        const e = c - Math.floor((1461 * d) / 4);
        const m = Math.floor((5 * e + 2) / 153);
        
        const day = e - Math.floor((153 * m + 2) / 5) + 1;
        const month = m + 3 - 12 * Math.floor(m / 10);
        const year = b * 100 + d - 4800 + Math.floor(m / 10);
        
        return { year, month, day };
    }

    // Convert date to Julian Day Number
    dateToJd(year, month, day) {
        const a = Math.floor((14 - month) / 12);
        const y = year + 4800 - a;
        const m = month + 12 * a - 3;
        
        return day + Math.floor((153 * m + 2) / 5) + 365 * y + Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400) - 32045;
    }

    // Get new moon day
    getNewMoonDay(k, timeZone = 7) {
        const T = k / 1236.85; // Time in Julian centuries from 1900 January 0.5
        const T2 = T * T;
        const T3 = T2 * T;
        const dr = Math.PI / 180;
        
        let Jd1 = 2415020.75933 + 29.53058868 * k + 0.0001178 * T2 - 0.000000155 * T3;
        Jd1 = Jd1 + 0.00033 * Math.sin((166.56 + 132.87 * T - 0.009173 * T2) * dr); // Mean new moon
        
        const M = 359.2242 + 29.10535608 * k - 0.0000333 * T2 - 0.00000347 * T3; // Sun's mean anomaly
        const Mpr = 306.0253 + 385.81691806 * k + 0.0107306 * T2 + 0.00001236 * T3; // Moon's mean anomaly
        const F = 21.2964 + 390.67050646 * k - 0.0016528 * T2 - 0.00000239 * T3; // Moon's argument of latitude
        
        let C1 = (0.1734 - 0.000393 * T) * Math.sin(M * dr) + 0.0021 * Math.sin(2 * dr * M);
        C1 = C1 - 0.4068 * Math.sin(Mpr * dr) + 0.0161 * Math.sin(dr * 2 * Mpr);
        C1 = C1 - 0.0004 * Math.sin(dr * 3 * Mpr);
        C1 = C1 + 0.0104 * Math.sin(dr * 2 * F) - 0.0051 * Math.sin(dr * (M + Mpr));
        C1 = C1 - 0.0074 * Math.sin(dr * (M - Mpr)) + 0.0004 * Math.sin(dr * (2 * F + M));
        C1 = C1 - 0.0004 * Math.sin(dr * (2 * F - M)) - 0.0006 * Math.sin(dr * (2 * F + Mpr));
        C1 = C1 + 0.0010 * Math.sin(dr * (2 * F - Mpr)) + 0.0005 * Math.sin(dr * (2 * Mpr + M));
        
        let deltat;
        if (T < -11) {
            deltat = 0.001 + 0.000839 * T + 0.0002261 * T2 - 0.00000845 * T3 - 0.000000081 * T * T3;
        } else {
            deltat = -0.000278 + 0.000265 * T + 0.000262 * T2;
        }
        
        const JdNew = Jd1 + C1 - deltat;
        return Math.floor(JdNew + 0.5 + timeZone / 24);
    }

    // Get sun longitude
    getSunLongitude(jdn, timeZone = 7) {
        const T = (jdn - 2451545.5 - timeZone / 24) / 36525; // Time in Julian centuries from 2000-01-01 12:00:00 GMT
        const T2 = T * T;
        const dr = Math.PI / 180; // degree to radian
        const M = 357.52910 + 35999.05030 * T - 0.0001559 * T2 - 0.00000048 * T * T2; // mean anomaly, degree
        const L0 = 280.46645 + 36000.76983 * T + 0.0003032 * T2; // mean longitude, degree
        let DL = (1.914600 - 0.004817 * T - 0.000014 * T2) * Math.sin(dr * M);
        DL = DL + (0.019993 - 0.000101 * T) * Math.sin(dr * 2 * M) + 0.000290 * Math.sin(dr * 3 * M);
        let L = L0 + DL; // true longitude, degree
        L = L * dr;
        L = L - Math.PI * 2 * (Math.floor(L / (Math.PI * 2))); // Normalize to (0, 2*PI)
        return Math.floor(L / Math.PI * 6);
    }

    // Get lunar month 11
    getLunarMonth11(yy, timeZone = 7) {
        const off = this.dateToJd(yy, 12, 31) - 2415021;
        const k = Math.floor(off / 29.530588853);
        let nm = this.getNewMoonDay(k, timeZone);
        const sunLong = this.getSunLongitude(nm, timeZone); // sun longitude at local midnight
        if (sunLong >= 9) {
            nm = this.getNewMoonDay(k - 1, timeZone);
        }
        return nm;
    }

    // Check if leap year
    getLeapMonthOffset(a11, timeZone = 7) {
        const k = Math.floor((a11 - 2415021.076998695) / 29.530588853 + 0.5);
        let last = 0;
        let i = 1; // We start with the month following lunar month 11
        let arc = this.getSunLongitude(this.getNewMoonDay(k + i, timeZone), timeZone);
        do {
            last = arc;
            i++;
            arc = this.getSunLongitude(this.getNewMoonDay(k + i, timeZone), timeZone);
        } while (arc !== last && i < 14);
        return i - 1;
    }

    // Convert solar date to lunar date
    solarToLunar(year, month, day, timeZone = 7) {
        const dayNumber = this.dateToJd(year, month, day);
        const k = Math.floor((dayNumber - 2415021.076998695) / 29.530588853);
        let monthStart = this.getNewMoonDay(k + 1, timeZone);
        
        if (monthStart > dayNumber) {
            monthStart = this.getNewMoonDay(k, timeZone);
        }
        
        const a11 = this.getLunarMonth11(year, timeZone);
        const b11 = a11;
        let lunarYear;
        
        if (a11 >= dayNumber) {
            lunarYear = year - 1;
        } else {
            lunarYear = year;
        }
        
        const a11Year = this.getLunarMonth11(lunarYear, timeZone);
        const diff = Math.floor((monthStart - a11Year) / 29);
        let lunarMonth = diff + 11;
        
        if (b11 - a11Year > 365) {
            const leapMonthDiff = this.getLeapMonthOffset(a11Year, timeZone);
            if (diff >= leapMonthDiff) {
                lunarMonth = diff + 10;
                if (diff === leapMonthDiff) {
                    lunarMonth = -lunarMonth; // Leap month
                }
            }
        }
        
        if (lunarMonth > 12) {
            lunarMonth = lunarMonth - 12;
        }
        if (lunarMonth <= 0) {
            lunarMonth = lunarMonth + 12;
        }
        
        const lunarDay = dayNumber - monthStart + 1;
        
        return {
            year: lunarYear,
            month: Math.abs(lunarMonth),
            day: lunarDay,
            isLeapMonth: lunarMonth < 0
        };
    }

    // Get Can Chi for year
    getYearCanChi(year) {
        const canIndex = (year + 6) % 10;
        const chiIndex = (year + 8) % 12;
        return this.can[canIndex] + " " + this.chi[chiIndex];
    }

    // Get Can Chi for month
    getMonthCanChi(year, month) {
        const canIndex = ((year % 5) * 2 + month - 1) % 10;
        const chiIndex = (month + 1) % 12;
        return this.can[canIndex] + " " + this.chi[chiIndex];
    }

    // Get Can Chi for day
    getDayCanChi(year, month, day) {
        const jd = this.dateToJd(year, month, day);
        const canIndex = (jd + 9) % 10;
        const chiIndex = (jd + 1) % 12;
        return this.can[canIndex] + " " + this.chi[chiIndex];
    }

    // Get zodiac animal for year
    getZodiac(year) {
        return this.zodiac[(year + 8) % 12];
    }

    // Get solar term for date
    getSolarTerm(month, day) {
        const termIndex = (month - 1) * 2;
        if (day < 15) {
            return this.solarTerms[termIndex];
        } else {
            return this.solarTerms[termIndex + 1];
        }
    }

    // Format lunar date
    formatLunarDate(lunarDate) {
        const monthName = this.lunarMonths[lunarDate.month - 1];
        const prefix = lunarDate.isLeapMonth ? "Nhuận " : "";
        return `${prefix}${monthName}, ngày ${lunarDate.day}`;
    }
}
